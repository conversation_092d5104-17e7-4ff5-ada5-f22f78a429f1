// arduino 1.8.15 (can not upload code due to boards.txt may not have upload.tool)
// 
//Need plugin accelstepper 1.61
#include <AccelStepper.h>
#include <EEPROM.h>

//#define maxStepSpeed 800 // 小轮
#define maxStepSpeed 3500   // 超大轮

// 定义各数据段在EEPROM中的存储地址
//const int ADDR_ID = 0;       // int (2字节)
//const int ADDR_TEMP = 2;     // float (4字节)
//const int ADDR_TIME = 6;     // long (4字节)
//const int ADDR_ACTIVE = 10;  // bool (1字节)
//const int ADDR_NAME = 11;    // 字符串 (16字节)


const int ADDR_positions = 0;       // int (2字节)
const int ADDR_extra = 2;
const int ADDR_maxStep = 4;     // long (4字节)
const int ADDR_reset_gap = 8;     // long (4字节)
const int ADDR_normal_gap = 12;     // long (4字节)
const int ADDR_hole_step = 16;


#define interruputPin_A 0

#define lightPin_A 4
#define switch_pin_s 5
int s_last =2;
int s_now  =2;
int s_led_val=0;
int s_led_pin_on = 9;
int s_led_pin_off = 10;
int led_counter=1;
#define switch_led_value_start 200
#define switch_led_value_end 255

#define maxHoleSize 16
// #define maxStep 9960

long circle_maxStep=1;
long position_normal_gap = 1;
long position_reset_gap=1;
int temp_wheel_max_position = 1;
int wheel_max_position = 1; // todo start from 0? 1?
long hole_gap_step=1;

#define moveAcceleration 6000 //need to check what limit the speed TODO is setMinPulseWidth?
#define searchStepSpeed (maxStepSpeed/2)

// #define minResetHoleGap 25
// #define maxResetHoleGap 55
// #define maxIngoreHoleGap 18 // may receive more than one interrupt during one hole
long minResetHoleGap = 25;
long maxResetHoleGap = 55;
long maxIngoreHoleGap = 18; // may receive more than one interrupt during one hole

#define minIngoreHoleGap_SlewCheck 10 // Ignore reset hole and other interrupt during one hole

// Define stepper motor connections and motor interface type. Motor interface type must be set to 1 when using a driver:
#define dirPin_A 6

#define stepPin_A 7

#define currentPin 8 // todo !!!!!!!!!!!!!
#define motorInterfaceType 1
#define versionNum 20250625




#define ledPin_G 10
#define ledPin_B 11
#define ledPin_R 9    // todo  led 的指示灯作用

#define stable_wheel_ms 40

int lastIndext_now_cmd = -1;

volatile int holeCount = 0;
volatile int fwHoleCount = 0;
volatile int incomingByte = 0; // for incoming serial data
AccelStepper stepper = AccelStepper(motorInterfaceType, stepPin_A, dirPin_A);
long holePosition[maxHoleSize];
long fwHolePositionSort[maxHoleSize];
volatile long preInterruptPosition = -100;
volatile long preHolePosition = -100;
volatile long preGapStep = -1;
int meetResetGapCounter = 0;
int lastIndext = -1;
volatile int stop_after_hole_count_A=-1;
unsigned long preTime;
long nextTargetPosition = 0;
int pos_gap_count_A = 0;
int pos_hole_count_A = 0;
int run_dir_A = 1;


volatile long holeCountSlewCheck_A=0;
volatile long preInterruptPositionSlewCheck_A = -100;
volatile long skip_first_hole_steps_A = 0;
volatile long skip_first_hole_steps_diff_A = 0;



volatile long holePositionDebug[maxHoleSize];
String cmdLine;

void setup() {
    pinMode(currentPin, OUTPUT);
    digitalWrite(currentPin,HIGH);

	Serial.setTimeout(10); //for serial fast response , change readString timeout
    Serial.begin(9600); // opens serial port, sets data rate to 9600 bps
    //while (!Serial);
    stepper.setMaxSpeed(maxStepSpeed);
    stepper.setMinPulseWidth(10);

    pinMode(lightPin_A,OUTPUT);
    pinMode(switch_pin_s,INPUT);



    pinMode(ledPin_G,OUTPUT);

	digitalWrite(ledPin_G,HIGH);


    delay(100);
    stepper.setPinsInverted(true,false,false);
    stepper.setAcceleration(moveAcceleration);



	

  
  // 从EEPROM读取数据
  readDataFromEEPROM();


    //resetHole();    // find holes between 
	fastResetHole();
	delay(300);

}


void calculate_short_path_and_dir_A(int startIdx, int endIdx){

	int speedAndDir = maxStepSpeed;
	run_dir_A = 1;
	bool pass_reset_hole = false;
	if(endIdx > startIdx){
		if(endIdx - startIdx > (wheel_max_position/2)){
			run_dir_A = -1;
		    speedAndDir = -1 * speedAndDir;
			pos_gap_count_A = wheel_max_position - endIdx + startIdx;
			pass_reset_hole = true;
		}else{
			run_dir_A = 1;
			speedAndDir = speedAndDir;
			pos_gap_count_A = endIdx - startIdx;
			pass_reset_hole = false;
		}
	}else if(endIdx < startIdx){
		if(startIdx - endIdx > (wheel_max_position/2)){
			run_dir_A = 1;
			speedAndDir = speedAndDir;
			pos_gap_count_A = wheel_max_position - startIdx + endIdx;
			pass_reset_hole = true;
		}else{
			run_dir_A = -1;
			speedAndDir = -1 * speedAndDir;
			pos_gap_count_A = startIdx - endIdx;
			pass_reset_hole = false;
		}
	}else{
		pos_gap_count_A = 0;
	}
	pos_hole_count_A = pos_gap_count_A + (pass_reset_hole? 1 : 0);
	//Serial.println(String(startIdx)  + " => " + endIdx );
	//Serial.println("c A  = "  + String(pass_reset_hole)  + " / " + pos_gap_count_A + " / " + pos_hole_count_A);
	stepper.setSpeed(speedAndDir);

}


void holeInterrupt_reset()
{
  digitalWrite(ledPin_B,LOW);
if(holeCount>0){ //filter interrupt after first hole interrupt
   if(stepper.currentPosition() - preInterruptPosition < maxIngoreHoleGap){
	   	digitalWrite(ledPin_B,HIGH);
		 //Serial.println("R   "+ String(stepper.currentPosition()) + " - " + String(preInterruptPosition)); 
       return;
   }
   preGapStep = stepper.currentPosition() - preInterruptPosition;
   
   if(minResetHoleGap < preGapStep && preGapStep < maxResetHoleGap){
       meetResetGapCounter++;
   }
}
 
  if(meetResetGapCounter == 1){
    fwHolePositionSort[fwHoleCount] = stepper.currentPosition() % circle_maxStep;
    fwHoleCount = fwHoleCount + 1;
  }
   holeCount = holeCount + 1;
  preInterruptPosition = stepper.currentPosition();
  digitalWrite(ledPin_B,HIGH);
  //Serial.println("-" + String(meetResetGapCounter)); 
  if(meetResetGapCounter >= 2){
	nextTargetPosition =  stepper.currentPosition() + (hole_gap_step/2);
    //stepper.stop();
	
	
  }
  
}

void fastResetInterrupt()
{
  digitalWrite(ledPin_B,LOW);
if(holeCount>0){ //filter interrupt after first hole interrupt
   if(stepper.currentPosition() - preInterruptPosition < maxIngoreHoleGap){
	   	digitalWrite(ledPin_B,HIGH);
		 //Serial.println("R   "+ String(stepper.currentPosition()) + " - " + String(preInterruptPosition)); 
       return;
   }
   preGapStep = stepper.currentPosition() - preInterruptPosition;
   
   if(minResetHoleGap < preGapStep && preGapStep < maxResetHoleGap){
       meetResetGapCounter++;
   }
}
 
  if(meetResetGapCounter == 1){
    fwHolePositionSort[fwHoleCount] = stepper.currentPosition() % circle_maxStep;
    fwHoleCount = fwHoleCount + 1;
  }
   holeCount = holeCount + 1;
  preInterruptPosition = stepper.currentPosition();
  digitalWrite(ledPin_B,HIGH);
  //Serial.println("-" + String(meetResetGapCounter)); 
  if(meetResetGapCounter >= 1){
	nextTargetPosition =  stepper.currentPosition() + (hole_gap_step/2);
    //stepper.stop();
	
	
  }
  
}


void resetSlewCheck(){
    skip_first_hole_steps_A = stepper.currentPosition();
	holeCountSlewCheck_A = 0;
	preInterruptPositionSlewCheck_A = stepper.currentPosition()-100;
}

volatile bool hole_edge_signal = false;

// 添加 fastResetHole 的状态管理变量
enum FastResetState {
  FAST_RESET_IDLE = 0,
  FAST_RESET_INIT = 1,
  FAST_RESET_FIRST_MOVE = 2,
  FAST_RESET_SECOND_MOVE = 3,
  FAST_RESET_FINISH = 4
};

volatile FastResetState fastResetState = FAST_RESET_IDLE;

void holeInterrupt_hole_max_step()
{
	hole_edge_signal = true;
	temp_wheel_max_position++;
}



void calc_hole_max_step_size(){
    digitalWrite(currentPin,LOW);
    detachInterrupt(interruputPin_A);
    attachInterrupt(interruputPin_A,holeInterrupt_hole_max_step,FALLING);
    //Serial.println("-1-");
	//开启红外
	digitalWrite(lightPin_A,HIGH);
	stepper.setSpeed(searchStepSpeed);
	
	
	while (LOW == digitalRead(interruputPin_A)) {
        stepper.runSpeed();
    }
	//Serial.println("-1.5-");
	delay(2000);
	long hole_gap = 0;
	hole_edge_signal = false;
	
	

	while (!hole_edge_signal) {
        stepper.runSpeed();
    }
	hole_gap = stepper.currentPosition();
	
    detachInterrupt(interruputPin_A);
    attachInterrupt(interruputPin_A,holeInterrupt_hole_max_step,RISING);
	
	hole_edge_signal = false;
	//Serial.println(String(hole_gap));
	//Serial.println("-2-");
	delay(2000);
	
	while (!hole_edge_signal) {
        stepper.runSpeed();
		
    }
	hole_gap = abs(stepper.currentPosition() - hole_gap);
	
	hole_gap_step = hole_gap;
	//Serial.println(String(hole_gap));
    //Serial.println("-3-");

	//stepper.moveTo(stepper.currentPosition() + hole_gap);
	
	hole_edge_signal = false;
	
	double now_gap=1;
	double last_gap=1;
	
	//stepper.setCurrentPosition(0);
	long last_position = stepper.currentPosition();
	double gap_percent = 0;
	gap_percent=now_gap/last_gap;
	
	//stepper.moveTo(stepper.currentPosition() + hole_gap);
	
	

	
	delay(500);
	
	//hole_edge_signal = false;
	//while (!hole_edge_signal) {
	//	stepper.runSpeed();
	//}
	
	while(gap_percent>0.5 || gap_percent<0.1){
		last_gap = now_gap;
		//Serial.println("-g_p 1-"+String(gap_percent));
		hole_edge_signal = false;
		while (!hole_edge_signal) {
			stepper.runSpeed();
		}

		now_gap = stepper.currentPosition() - last_position;
		last_position = stepper.currentPosition();
		gap_percent=now_gap/last_gap;
		//Serial.println("-g_p 2-"+String(gap_percent));
		//Serial.println("-g_p 3-"+String(now_gap)+"/" + String(last_gap));

		
	}
	
	Serial.println("-g_p 5-"+String(gap_percent));
	position_normal_gap = last_gap;
	position_reset_gap = now_gap;
	
	//计算一圈步数 和最大孔槽数
	delay(200);
	temp_wheel_max_position=0;
	Serial.println("-max-");
	now_gap=1;
	last_gap=1;
	last_position = stepper.currentPosition();
	gap_percent=now_gap/last_gap;
	long last_max_position = stepper.currentPosition();
	
		
	while(gap_percent>0.5 || gap_percent<0.1){
		last_gap = now_gap;
		//Serial.println("-g_p 1-"+String(gap_percent));
		hole_edge_signal = false;
		while (!hole_edge_signal) {
			stepper.runSpeed();
		}

		now_gap = stepper.currentPosition() - last_position;
		last_position = stepper.currentPosition();
		gap_percent=now_gap/last_gap;
	}
	
	circle_maxStep = stepper.currentPosition() - last_max_position;
	wheel_max_position = temp_wheel_max_position;
	
	Serial.println("-max-" + String(circle_maxStep));
	Serial.println("-max p-" + String(wheel_max_position));

    writeDataToEEPROM(wheel_max_position-1, hole_gap_step, circle_maxStep, position_reset_gap, position_normal_gap);
	//关闭红外
	digitalWrite(lightPin_A,LOW);
	//恢复速度
	stepper.setSpeed(maxStepSpeed);
	digitalWrite(currentPin,HIGH);
}



void holeInterrupt_stop_at_hole()
{
//if(holeCountSlewCheck_A>0){ //filter interrupt after first hole interrupt
   if(abs(stepper.currentPosition() - preInterruptPositionSlewCheck_A) < minIngoreHoleGap_SlewCheck){
	   //digitalWrite(ledPin_B,HIGH);
	   //Serial.println("K1:");
       return;
   }
//}
	digitalWrite(ledPin_B,LOW);
  skip_first_hole_steps_diff_A = abs(skip_first_hole_steps_A - stepper.currentPosition());
  if(skip_first_hole_steps_diff_A > 20 ){   //走出大于一个孔位（小于一个reset间距）之后 开始计数，用于忽略当前孔位
    holeCountSlewCheck_A = holeCountSlewCheck_A + 1;
    preInterruptPositionSlewCheck_A = stepper.currentPosition();
	//Serial.println("K2:"+String(skip_first_hole_steps_diff_A)+"  "+String(skip_first_hole_steps_A)+" "+String(stepper.currentPosition()));
  }else{
	  //Serial.println("K:"+String(skip_first_hole_steps_diff_A)+"  "+String(skip_first_hole_steps_A)+" "+String(stepper.currentPosition()));
	  	//Serial.println(" K: " + String(abs(skip_first_hole_steps_A - stepper.currentPosition())));
	  	//Serial.println(" K: " + String(holeCountSlewCheck_A) + " >= " + String(pos_hole_count_A) );
  }
  
  if(holeCountSlewCheck_A >= pos_hole_count_A && holeCountSlewCheck_A >0){
    nextTargetPosition =  stepper.currentPosition() + (run_dir_A * (hole_gap_step/2));
    //stepper.stop();
	//stepper.runToPosition();
	//Serial.println(" s: " + String(abs(skip_first_hole_steps_A - stepper.currentPosition())));
	//Serial.println(" P: " + String(stepper.currentPosition()));
  }
  digitalWrite(ledPin_B,HIGH);
}


void resetHole(){
stepper.setPinsInverted(false,false,false);
minResetHoleGap = position_reset_gap - (position_reset_gap/4) ;
maxResetHoleGap = position_reset_gap + (position_reset_gap/4);
maxIngoreHoleGap = hole_gap_step;
//Serial.println("reset: " + String(minResetHoleGap) + " - " + String(maxResetHoleGap) + " - " + String(maxIngoreHoleGap));


  digitalWrite(currentPin,LOW);
// Serial.println("=======================================2 "); //!!! too much output will cause arduino lock !!!
  detachInterrupt(interruputPin_A); // detach protential  holeInterrupt_slewCheck
  holeCount = 0;
  fwHoleCount = 0;
  meetResetGapCounter = 0;
  stepper.setCurrentPosition(0);
  preInterruptPosition = -100;

  attachInterrupt(interruputPin_A,holeInterrupt_reset,FALLING);
  digitalWrite(lightPin_A,HIGH);
  
  for (byte i = 0; i < maxHoleSize; i++) {
	  holePosition[i] = -1;
          fwHolePositionSort[i] = -1;
    }
    delay(200);
    
	stepper.setSpeed(maxStepSpeed);
    nextTargetPosition = circle_maxStep * 3;
	
	while (stepper.currentPosition() != nextTargetPosition)
	{
		stepper.runSpeed();
	}
    
    digitalWrite(lightPin_A,LOW);
    detachInterrupt(interruputPin_A);
    
    lastIndext = 0;
	attachInterrupt(interruputPin_A,holeInterrupt_stop_at_hole,FALLING); 
	resetSlewCheck(); // reset slew check
    //Serial.print(0);
	delay(stable_wheel_ms);
    digitalWrite(currentPin,HIGH);
}

// 启动非阻塞的 fastResetHole 过程
void fastResetHole(){
  fastResetState = FAST_RESET_INIT;
}

// 非阻塞的 fastResetHole 状态处理函数
void processFastResetHole(){
  switch(fastResetState){
    case FAST_RESET_IDLE:
      // 空闲状态，什么都不做
      break;

    case FAST_RESET_INIT:
      // 初始化阶段
      stepper.setPinsInverted(true,false,false);
      minResetHoleGap = position_reset_gap - (position_reset_gap/4) ;
      maxResetHoleGap = position_reset_gap + (position_reset_gap/4);
      maxIngoreHoleGap = hole_gap_step;

      digitalWrite(currentPin,LOW);
      detachInterrupt(interruputPin_A); // detach protential  holeInterrupt_slewCheck
      holeCount = 0;
      fwHoleCount = 0;
      meetResetGapCounter = 0;
      stepper.setCurrentPosition(0);
      preInterruptPosition = -100;

      attachInterrupt(interruputPin_A,fastResetInterrupt,FALLING);
      digitalWrite(lightPin_A,HIGH);

      for (byte i = 0; i < maxHoleSize; i++) {
        holePosition[i] = -1;
        fwHolePositionSort[i] = -1;
      }
      delay(200);

      stepper.setSpeed(maxStepSpeed);
      nextTargetPosition = circle_maxStep * 3;
      fastResetState = FAST_RESET_FIRST_MOVE;
      break;

    case FAST_RESET_FIRST_MOVE:
      // 第一阶段移动：非阻塞地运行到目标位置
      if (stepper.currentPosition() != nextTargetPosition) {
        stepper.runSpeed();
      } else {
        // 第一阶段完成，准备第二阶段
        nextTargetPosition = stepper.currentPosition() + position_normal_gap + (position_normal_gap/2);
        fastResetState = FAST_RESET_SECOND_MOVE;
      }
      break;

    case FAST_RESET_SECOND_MOVE:
      // 第二阶段移动：非阻塞地运行到目标位置
      if (stepper.currentPosition() != nextTargetPosition) {
        stepper.runSpeed();
      } else {
        // 第二阶段完成，进入完成阶段
        fastResetState = FAST_RESET_FINISH;
      }
      break;

    case FAST_RESET_FINISH:
      // 完成阶段
      digitalWrite(lightPin_A,LOW);
      detachInterrupt(interruputPin_A);

      lastIndext = 0;
      attachInterrupt(interruputPin_A,holeInterrupt_stop_at_hole,FALLING);
      resetSlewCheck(); // reset slew check
      delay(50);

      digitalWrite(lightPin_A,HIGH);
      delay(stable_wheel_ms);
      digitalWrite(currentPin,HIGH);

      fastResetState = FAST_RESET_IDLE; // 重置为空闲状态
      break;
  }
}


void writeDataToEEPROM(int mem_position_counter, long mem_hole_step, long mem_circle_maxStep, long mem_position_reset_gap, long mem_position_normal_gap) {
  // 分别写入各数据段
  EEPROM.put(ADDR_positions, mem_position_counter);
  EEPROM.put(ADDR_hole_step, mem_hole_step);
  EEPROM.put(ADDR_maxStep, mem_circle_maxStep);
  EEPROM.put(ADDR_reset_gap, mem_position_reset_gap);
  EEPROM.put(ADDR_normal_gap, mem_position_normal_gap);

  Serial.println("Data written to EEPROM:");
  Serial.print("pos: "); Serial.println(mem_position_counter);
  Serial.print("h_step: "); Serial.println(mem_hole_step);
  Serial.print("max_step: "); Serial.println(mem_circle_maxStep);
  Serial.println("-----------------------");
}

void readDataFromEEPROM() {
  // 分别读取各数据段
  EEPROM.get(ADDR_positions, wheel_max_position);
  EEPROM.get(ADDR_hole_step, hole_gap_step);
  EEPROM.get(ADDR_maxStep, circle_maxStep);
  EEPROM.get(ADDR_reset_gap, position_reset_gap);
  EEPROM.get(ADDR_normal_gap, position_normal_gap);

  //Serial.println("Data read from EEPROM:");
  //Serial.print("pos: "); Serial.println(wheel_max_position);
  //Serial.print("h_step: "); Serial.println(hole_gap_step);
  //Serial.print("max_step: "); Serial.println(circle_maxStep);

  //Serial.println("-----------------------");
}

void serial_switch_led_no_interrupt(){
	led_counter++;
	s_now = digitalRead(switch_pin_s);
	if(s_now==s_last){
		if(led_counter%200==0){
		  s_led_val++;
		  s_led_val = s_led_val>switch_led_value_end? switch_led_value_end:s_led_val;
		}
		
	}else{
	  s_led_val = switch_led_value_start;  
	  s_last = s_now;
	  s_led_pin_on = s_now ==HIGH? ledPin_R:ledPin_G;
	  s_led_pin_off = s_now ==HIGH? ledPin_G:ledPin_R;
	}


	analogWrite(s_led_pin_on, s_led_val);
	analogWrite(s_led_pin_off, switch_led_value_end);
}

void loop() {
	serial_switch_led_no_interrupt();

	// 处理非阻塞的 fastResetHole 状态
	processFastResetHole();

    if (Serial.available() > 0) {
        cmdLine = Serial.readString();
        //Serial.println(cmdLine);
		//Serial.println(" B 0 =>  "  + cmdLine);
        if (cmdLine == "version" || cmdLine == "VRS") {
			
            Serial.print(versionNum);
        }else if (cmdLine == "MXP") {
	      Serial.print(String(wheel_max_position));
        }else if (cmdLine == "NOW") {
	      Serial.print(String(lastIndext_now_cmd));
        }else if (cmdLine == "debug") {
          for(byte i = 0; i < maxHoleSize; i++){
             Serial.println(String(i) + "  pos = " + String(fwHolePositionSort[i]));	
          }
            Serial.println("****************");	
            //for(byte i = 0; i < maxHoleSize; i++){
            //     Serial.println(String(i) + " : " + String(holePosition[i]));	
            //}

            Serial.println("****************");	

//            Serial.println("--------------");	 
//            for(byte i = 0; i < maxHoleSize; i++){
//                 Serial.println(String(i) + "ingore : " + String(ignoreInterGap[i]));	
//            }
        }
        else if(cmdLine == "reset" || cmdLine == "RESET"){
          //resetHole();
		  fastResetHole();
        }else if(cmdLine == "init"){
            calc_hole_max_step_size();
        }else if(cmdLine == "clr_mem"){
            writeDataToEEPROM(0,0,0,0,0);
        }
        if(cmdLine.length() == 1){
          // 检查是否正在执行 fastResetHole，如果是则忽略运动指令
          if(fastResetState != FAST_RESET_IDLE){
            Serial.println("BUSY"); // 返回忙碌状态
          } else {
            digitalWrite(currentPin,LOW);
          //Serial.println("goto  = " + cmdLine + "  " + String(fwHolePositionSort[cmdLine.toInt()]));
          if(cmdLine.toInt()<0 || cmdLine.toInt()>15){
            Serial.println("Error Goto = " + cmdLine + "  " + String(fwHolePositionSort[cmdLine.toInt()]));
          }

          preTime = millis();
          int targetIndex = cmdLine.toInt();

		  resetSlewCheck();
		  digitalWrite(lightPin_A,HIGH);
		  //Serial.println(" B  1=>  "  + cmdLine);
		  //delay(10);

          //Serial.println("goto A  = "  + String(targetIndex));

          calculate_short_path_and_dir_A(lastIndext, targetIndex);
          nextTargetPosition = stepper.currentPosition() + (run_dir_A * pos_gap_count_A * (position_normal_gap + (position_normal_gap/2)));
          skip_first_hole_steps_A = stepper.currentPosition();
          //Serial.println("pass A  = "  + String(pos_hole_count_A)  + " / " + pos_gap_count_A  + " / " + nextTargetPosition );
          delay(10); //等待中断稳定

          if(run_dir_A>0){ //拆开写是为了循环效率高一点
              while (stepper.currentPosition() < nextTargetPosition)
              {
                  stepper.runSpeed();
              }
          }else{
              while (stepper.currentPosition() > nextTargetPosition)
              {
                  stepper.runSpeed();
              }
          }


          //Serial.println("E  = "  + String(stepper.currentPosition()));
          lastIndext = targetIndex;
          lastIndext_now_cmd = lastIndext;
			

		  digitalWrite(lightPin_A,LOW);
          delay(stable_wheel_ms); 

		  stepper.setCurrentPosition(0);//防止多圈超最大位置

		  Serial.print(cmdLine.toInt());
		  
//          Serial.println(millis() - preTime);

           digitalWrite(currentPin,HIGH);
        }



    }
	
}